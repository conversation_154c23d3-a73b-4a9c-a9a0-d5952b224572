// 平板端触摸优化样式
// 专门为平板设备的触摸交互进行优化

// 全局触摸目标最小尺寸
$touch-target-min: 44px;
$touch-target-comfortable: 56px;
$touch-target-large: 72px;

// 触摸反馈动画
@mixin touch-feedback($scale: 0.95, $duration: 0.1s) {
  transition: transform $duration ease;
  
  &:active {
    transform: scale($scale);
  }
}

// 触摸涟漪效果
@mixin touch-ripple($color: var(--primary)) {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: $color;
    border-radius: 50%;
    opacity: 0;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
  }
  
  &:active::before {
    width: 100%;
    height: 100%;
    opacity: 0.1;
  }
}

// 平板端专用选择器优化
@media (min-width: $mobile) and (max-width: $tablet) {
  // 全局按钮优化
  button, .btn, [role="button"] {
    min-height: $touch-target-comfortable;
    min-width: $touch-target-comfortable;
    padding: $spacing-md $spacing-lg;
    border-radius: 12px;
    font-size: $font-size-base;
    font-weight: 600;
    
    @include touch-feedback();
    @include touch-ripple();
  }

  // 输入框优化
  input, textarea, select {
    min-height: $touch-target-comfortable;
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-base;
    border-radius: 12px;
    border-width: 2px;
  }

  // 链接优化
  a {
    min-height: $touch-target-min;
    padding: $spacing-sm $spacing-md;
    display: inline-flex;
    align-items: center;
    
    @include touch-feedback(0.98);
  }

  // 选择项优化
  .option-item, .choice-item, .selection-item {
    min-height: $touch-target-large;
    padding: $spacing-lg $spacing-xl;
    margin: $spacing-sm 0;
    border-radius: 16px;
    border-width: 2px;
    
    @include touch-feedback();
    @include touch-ripple();
    
    // 增强视觉反馈
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
    
    &.selected, &.active {
      transform: scale(1.02);
      box-shadow: var(--shadow-xl);
    }
  }

  // 卡片容器优化
  .card, .panel, .section {
    border-radius: 16px;
    padding: $spacing-xl;
    
    &.clickable {
      @include touch-feedback();
      cursor: pointer;
    }
  }

  // 导航元素优化
  .nav-item, .menu-item, .tab-item {
    min-height: $touch-target-comfortable;
    padding: $spacing-md $spacing-lg;
    
    @include touch-feedback();
  }

  // 表单控件优化
  .form-control, .input-group {
    .control-label {
      font-size: $font-size-lg;
      margin-bottom: $spacing-md;
    }
    
    .control-input {
      min-height: $touch-target-comfortable;
      font-size: $font-size-base;
    }
  }

  // 模态框和弹窗优化
  .modal, .popup, .dialog {
    .modal-header {
      padding: $spacing-xl;
      min-height: $touch-target-large;
    }
    
    .modal-content {
      padding: $spacing-xl;
    }
    
    .modal-footer {
      padding: $spacing-xl;
      gap: $spacing-lg;
      
      .modal-button {
        min-height: $touch-target-comfortable;
        min-width: 120px;
        font-size: $font-size-base;
      }
    }
  }

  // 列表项优化
  .list-item, .menu-option {
    min-height: $touch-target-large;
    padding: $spacing-lg $spacing-xl;
    
    @include touch-feedback();
    
    .item-icon {
      width: 32px;
      height: 32px;
      margin-right: $spacing-lg;
    }
    
    .item-text {
      font-size: $font-size-base;
      line-height: 1.4;
    }
  }

  // 切换开关优化
  .toggle, .switch, .checkbox {
    min-width: $touch-target-min;
    min-height: $touch-target-min;
    
    &::before {
      width: 24px;
      height: 24px;
    }
  }

  // 滑块控件优化
  .slider, .range {
    height: 8px;
    
    .slider-thumb {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      box-shadow: var(--shadow-lg);
    }
  }

  // 标签页优化
  .tab-bar {
    .tab-button {
      min-height: $touch-target-comfortable;
      padding: $spacing-md $spacing-xl;
      font-size: $font-size-base;
      
      @include touch-feedback();
    }
  }

  // 工具栏优化
  .toolbar {
    .toolbar-button {
      min-width: $touch-target-comfortable;
      min-height: $touch-target-comfortable;
      padding: $spacing-md;
      
      @include touch-feedback();
    }
  }

  // 分页控件优化
  .pagination {
    .page-button {
      min-width: $touch-target-min;
      min-height: $touch-target-min;
      margin: 0 $spacing-xs;
      
      @include touch-feedback();
    }
  }

  // 评分控件优化
  .rating, .star-rating {
    .rating-item {
      width: $touch-target-min;
      height: $touch-target-min;
      margin: 0 $spacing-xs;
      
      @include touch-feedback();
    }
  }

  // 手风琴优化
  .accordion {
    .accordion-header {
      min-height: $touch-target-large;
      padding: $spacing-lg $spacing-xl;
      
      @include touch-feedback();
    }
    
    .accordion-toggle {
      width: $touch-target-comfortable;
      height: $touch-target-comfortable;
    }
  }

  // 下拉菜单优化
  .dropdown {
    .dropdown-toggle {
      min-height: $touch-target-comfortable;
      padding: $spacing-md $spacing-lg;
      
      @include touch-feedback();
    }
    
    .dropdown-item {
      min-height: $touch-target-min;
      padding: $spacing-md $spacing-lg;
      
      @include touch-feedback();
    }
  }
}

// 触摸设备专用样式
@media (pointer: coarse) {
  // 移除悬停效果，因为触摸设备没有真正的悬停
  * {
    &:hover {
      // 保留必要的悬停效果，但减少依赖
    }
  }
  
  // 增强焦点指示器
  *:focus {
    outline: 3px solid var(--primary);
    outline-offset: 2px;
  }
  
  // 优化滚动条
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
  
  ::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: var(--muted-foreground);
    min-height: 40px;
  }
}
